{"name": "lazy-cache", "description": "Cache requires to be lazy-loaded when needed.", "version": "0.2.7", "homepage": "https://github.com/jonschlinkert/lazy-cache", "author": "<PERSON> (https://github.com/jonschlinkert)", "repository": "jonschlinkert/lazy-cache", "bugs": {"url": "https://github.com/jonschlinkert/lazy-cache/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "devDependencies": {"ansi-yellow": "^0.1.1", "glob": "^5.0.14", "mocha": "*"}, "keywords": ["cache", "caching", "dependencies", "dependency", "lazy", "require", "requires"], "verb": {"related": {"list": ["lint-deps"]}, "plugins": ["gulp-format-md"]}}